class PrepareBulkDeleteContentsWorker
  include Sidekiq::Worker
  sidekiq_options queue: :bulk_deletion_setup, retry: 2

  def perform(company_id, bulk_destroying_content_params, content_ids = [], only_actives = false)
    tenant = get_tenant(company_id)

    raise "Tenant not found company_id: #{company_id}" if tenant.nil?

    bulk_destroying_content = BulkDestroyingContent.create!(bulk_destroying_content_params)
    bulk_destroying_content_id = bulk_destroying_content.id

    Apartment::Tenant.switch(tenant) do
      contents = get_all_contents(bulk_destroying_content_params['business_id'], only_actives, content_ids)

      contents.find_in_batches(batch_size: 10_000) do |batch|
        content_destroyings_data = batch.pluck(:id).map do |content_id|
          { content_id:, bulk_destroying_content_id: }
        end

        Apartment::Tenant.switch('public') do
          bulk_destroying_content.content_destroyings.insert_all(content_destroyings_data)
        end
      end
    end

    BulkDeleteContentsWorker.perform_async(tenant, bulk_destroying_content_id)
  end

  private

  def get_tenant(company_id)
    Company.find(company_id)&.subdomain
  end

  def get_all_contents(business_id, only_actives, content_ids = [])
    base_scope = only_actives || content_ids.present? ? Content.all : Content.with_discarded.discarded
    base_scope = base_scope.select(:id)
    contents = base_scope.not_draft.where(business_id:)
    contents = contents.where(id: content_ids) if content_ids.present?
    contents
  end
end
