require 'thor'
require 'byebug'
require 'net/ssh'
require 'net/scp'
require 'English'

module Fourmdg
  class CLI < Thor
    include Thor::Actions

    HOSTS = {
      mdmacademy: 'fourmdg-sandbox-aurora-cluster.cluster-c7mmsyhovoiu.us-east-1.rds.amazonaws.com',
      sandbox: 'fourmdg-sandbox-aurora-cluster.cluster-c7mmsyhovoiu.us-east-1.rds.amazonaws.com',
      staging: 'fourmdg-staging.c7mmsyhovoiu.us-east-1.rds.amazonaws.com',
      production: 'fourmdg-production.c7mmsyhovoiu.us-east-1.rds.amazonaws.com',
      uat: 'fourmdg-sandbox-aurora-cluster.cluster-c7mmsyhovoiu.us-east-1.rds.amazonaws.com'
    }.freeze
    USER = 'postgres'.freeze
    PASSWORD = 'I5EgpXdpgwvr8W'.freeze
    DATABASES = {
      mdmacademy: 'mdmacademy',
      sandbox: 'fourmdg_sandbox',
      staging: 'fourmdg_staging',
      production: 'fourmdg-api_production',
      uat: 'fourmdg_uat',
    }.freeze
    REMOTE_DUMP_HOSTS = {
      mdmacademy: '**************',
      sandbox: '**************',
      staging: '**************',
      production: '**************',
      uat: '**************'
    }.freeze
    REMOTE_DUMP_USER = 'ubuntu'.freeze
    REMOTE_DUMP_KEY = './keys/database_dump_instance.pem'.freeze
    DUMP_FILE = 'fourmdg.dump'.freeze

    def self.exit_on_failure?
      true
    end

    desc 'dump', 'Efetua o dump do banco escolhido'
    def dump
      environment = ask 'Informe o ambiente que deseja fazer o dump:', %i[blue bold], limited_to: HOSTS.keys.map(&:to_s)

      say 'Listando as bases disponíveis...'

      Net::SSH.start(REMOTE_DUMP_HOSTS[environment.to_sym], REMOTE_DUMP_USER, keys: REMOTE_DUMP_KEY) do |ssh|
        result = ''

        ssh.exec!("PGPASSWORD=#{PASSWORD} psql -h #{HOSTS[environment.to_sym]} -U #{USER} -d #{DATABASES[environment.to_sym]} -c 'SELECT subdomain FROM public.companies'") do |_ch, stream, data|
          say data.force_encoding('UTF-8')

          result << data if stream == :stdout
        end

        subdomains = []

        result.each_line.with_index do |line, index|
          next if [0, 1, result.lines.size - 1, result.lines.size - 2].include?(index)

          subdomains << line.strip
        end

        selected = []

        subdomains = subdomains.sort

        select_subdomain(selected, subdomains - selected)
        select_subdomain(selected, subdomains - selected) while yes? 'Deseja escolher mais clientes? (digite y ou yes para selecionar mais)', %i[yellow bold]

        selected.unshift('shared_extensions')
        selected.unshift('public')

        say "\r\nEfetuado dump dos schemas #{selected.join(', ')}..."

        to_skip = (subdomains - selected).map { |str| "-N #{str}" }.join(' ')

        result = ssh.exec!("PGPASSWORD=#{PASSWORD} pg_dump -h #{HOSTS[environment.to_sym]} -U #{USER} -d #{DATABASES[environment.to_sym]} -v -O -Fc -f #{DUMP_FILE} #{to_skip}") do |_ch, _stream, data|
          say data.force_encoding('UTF-8')
        end

        say "\r\nDump gerado com sucesso.", %i[green bold]
      end

      Net::SCP.download!(REMOTE_DUMP_HOSTS[environment.to_sym], REMOTE_DUMP_USER, "/home/<USER>/#{DUMP_FILE}", DUMP_FILE, ssh: { keys: REMOTE_DUMP_KEY }) do |_ch, name, sent, total|
        say "\rTransferindo #{name}...#{sent}/#{total} "
      end

      Net::SSH.start(REMOTE_DUMP_HOSTS[environment.to_sym], REMOTE_DUMP_USER, keys: REMOTE_DUMP_KEY) do |ssh|
        say "\r\nApagando dump...", %i[yellow bold]
        ssh.exec!("rm -rf /home/<USER>/#{DUMP_FILE}")
      end

      say "\r\Arquivo #{DUMP_FILE} transferido com sucesso.", %i[green bold]
      say 'Para restaurar execute: ', %i[green bold]
      say "pg_restore --clean -d fourmdg_development -h localhost -U postgres -v -O < #{DUMP_FILE}", %i[cyan bold]
    end

    desc 'dump_tenant TENANT', 'Efetua o dump de um tenant específico do ambiente UAT'
    def dump_tenant(tenant_name)
      environment = :uat
      dump_file = "#{tenant_name}_uat.dump"

      say "Gerando dump do tenant '#{tenant_name}' do ambiente UAT...", %i[blue bold]

      # Verificar se o tenant existe
      Net::SSH.start(REMOTE_DUMP_HOSTS[environment], REMOTE_DUMP_USER, keys: REMOTE_DUMP_KEY) do |ssh|
        result = ''

        ssh.exec!("PGPASSWORD=#{PASSWORD} psql -h #{HOSTS[environment]} -U #{USER} -d #{DATABASES[environment]} -c \"SELECT subdomain FROM public.companies WHERE subdomain = '#{tenant_name}'\"") do |_ch, stream, data|
          result << data if stream == :stdout
        end

        if result.include?(tenant_name)
          say "Tenant '#{tenant_name}' encontrado. Iniciando dump...", %i[green bold]
        else
          say "Erro: Tenant '#{tenant_name}' não encontrado no ambiente UAT!", %i[red bold]
          return
        end

        # Gerar dump apenas dos schemas necessários: public, shared_extensions e o tenant específico
        schemas_to_include = "-n public -n shared_extensions -n #{tenant_name}"

        say "Executando dump dos schemas: public, shared_extensions, #{tenant_name}...", %i[yellow bold]

        ssh.exec!("PGPASSWORD=#{PASSWORD} pg_dump -h #{HOSTS[environment]} -U #{USER} -d #{DATABASES[environment]} -v -O -Fc -f #{dump_file} #{schemas_to_include}") do |_ch, _stream, data|
          say data.force_encoding('UTF-8')
        end

        say "\r\nDump gerado com sucesso.", %i[green bold]
      end

      # Baixar o arquivo
      Net::SCP.download!(REMOTE_DUMP_HOSTS[environment], REMOTE_DUMP_USER, "/home/<USER>/#{dump_file}", dump_file, ssh: { keys: REMOTE_DUMP_KEY }) do |_ch, name, sent, total|
        say "\rTransferindo #{name}...#{sent}/#{total} "
      end

      # Limpar arquivo remoto
      Net::SSH.start(REMOTE_DUMP_HOSTS[environment], REMOTE_DUMP_USER, keys: REMOTE_DUMP_KEY) do |ssh|
        say "\r\nApagando dump remoto...", %i[yellow bold]
        ssh.exec!("rm -rf /home/<USER>/#{dump_file}")
      end

      say "\r\nArquivo #{dump_file} transferido com sucesso.", %i[green bold]
      say 'Para restaurar execute: ', %i[green bold]
      say "pg_restore --clean -d fourmdg_development -h localhost -U postgres -v -O < #{dump_file}", %i[cyan bold]
      say "\r\nOu usando Docker:", %i[green bold]
      say "1. Mova o arquivo para a pasta backups/", %i[cyan bold]
      say "2. docker compose up db", %i[cyan bold]
      say "3. docker compose exec db sh", %i[cyan bold]
      say "4. pg_restore -d fourmdg_development -h localhost -U postgres -v --no-owner < /var/lib/postgresql/backups/#{dump_file}", %i[cyan bold]
    end

    private

    def select_subdomain(selected, available)
      subdomain = ask 'Informe o cliente que deseja incluir no dump:', %i[blue bold], limited_to: available

      selected << subdomain
    end
  end
end
