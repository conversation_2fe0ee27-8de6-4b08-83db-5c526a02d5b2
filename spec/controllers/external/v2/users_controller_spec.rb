require 'rails_helper'

RSpec.describe External::V2::UsersController, type: :controller do
  let(:user) { create(:user) }
  let(:administrator) { create(:administrator, authorization_token: SecureRandom.hex(32)) }

  before do
    request.headers['ADMIN_TOKEN'] = administrator.authorization_token
    request.headers['ADMIN_EMAIL'] = administrator.email
  end

  describe 'POST #create' do
    let(:valid_params) do
      {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Admin123456789!@',
        password_confirmation: 'Admin123456789!@',
        limited: false,
        coordinator: false,
        notification: false,
        approved: true,
        confirmed_at: Time.zone.now,
        chat_enabled: false,
        block_menus: [],
        department_ids: []
      }
    end

    context 'with invalid provider' do
      let(:invalid_providers) { ['invalid_provider', 'facebook', 'twitter', 'linkedin', 'github'] }

      it 'returns unprocessable entity status for invalid providers' do
        invalid_providers.each do |provider|
          post :create, format: :json, params: valid_params.merge(provider: provider)

          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      it 'returns the invalid provider error message' do
        post :create, format: :json, params: valid_params.merge(provider: 'invalid_provider')

        expect(JSON.parse(response.body)['errors']).to eq(I18n.t("api.v2.users.errors.invalid_provider"))
      end

      it 'does not call UserService for invalid providers' do
        expect(UserService).not_to receive(:new)

        post :create, format: :json, params: valid_params.merge(provider: 'invalid_provider')
      end
    end

    context 'with valid provider' do
      let(:valid_providers) { ['email', 'google_oauth2', 'entra_id', 'openid_connect'] }
      let(:service) { double(:service, create_unconfirmed: true, success: true, errors: [], record: double(id: SecureRandom.uuid)) }

      before { allow(UserService).to receive(:new).and_return(service) }

      it 'allows valid providers' do
        valid_providers.each do |provider|
          post :create, format: :json, params: valid_params.merge(provider: provider)

          expect(response).to have_http_status(:created)
        end
      end

      it 'allows blank provider values' do
        [nil, ''].each do |provider|
          post :create, format: :json, params: valid_params.merge(provider: provider)

          expect(response).to have_http_status(:created)
        end
      end

      it 'calls UserService for valid providers' do
        post :create, format: :json, params: valid_params.merge(provider: 'email')

        expect(service).to have_received(:create_unconfirmed)
      end
    end

    context 'when provider is email' do
      context 'with missing password' do
        it 'returns unprocessable entity with password required error' do
          post :create, format: :json, params: valid_params.merge(
            provider: 'email',
            password: '',
            password_confirmation: 'Admin123456789!@'
          )

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)['errors']).to include(I18n.t("api.v2.users.errors.password_required"))
        end
      end

      context 'with missing password confirmation' do
        it 'returns unprocessable entity with confirmation password required error' do
          post :create, format: :json, params: valid_params.merge(
            provider: 'email',
            password: 'Admin123456789!@',
            password_confirmation: ''
          )

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)['errors']).to include(I18n.t("api.v2.users.errors.confirmation_password_required"))
        end
      end

      context 'with both password and confirmation missing' do
        it 'returns unprocessable entity with both error messages' do
          post :create, format: :json, params: valid_params.merge(
            provider: 'email',
            password: '',
            password_confirmation: ''
          )

          expect(response).to have_http_status(:unprocessable_entity)
          response_errors = JSON.parse(response.body)['errors']
          expect(response_errors).to include(I18n.t("api.v2.users.errors.password_required"))
          expect(response_errors).to include(I18n.t("api.v2.users.errors.confirmation_password_required"))
        end
      end

      context 'with valid password and confirmation' do
        it 'proceeds with user creation' do
          service = double(:service, create_unconfirmed: true, success: true, errors: [], record: double(id: SecureRandom.uuid))
          allow(UserService).to receive(:new).and_return(service)

          post :create, format: :json, params: valid_params.merge(provider: 'email')

          expect(response).to have_http_status(:created)
        end
      end
    end

    context 'with valid attributes' do
      it 'returns created status' do
        post :create, format: :json, params: valid_params
        expect(response).to have_http_status(:created)
      end
    end

    context 'with invalid attributes' do
      it 'returns unprocessable entity status' do
        post :create, format: :json, params: {
          name: 'Test User',
          email: ''
        }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context 'with invalid token' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
      end

      it 'returns unauthorized status' do
        post :create, format: :json, params: {
          name: 'Test User',
          email: ''
        }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'PUT #update' do
    context 'with valid attributes' do
      it 'returns ok status' do
        put :update, format: :json, params: {
          name: 'Test User',
          email: user.email,
          password: 'Admin123456789!@',
          password_confirmation: 'Admin123456789!@',
          limited: false,
          coordinator: false,
          notification: false,
          approved: true,
          confirmed_at: Time.zone.now,
          chat_enabled: false,
          block_menus: [],
          department_ids: []
        }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid attributes' do
      it 'returns unprocessable entity status' do
        put :update, format: :json, params: {
          name: 'Test User',
          email: '<EMAIL>'
        }
        expect(response).to have_http_status(:not_found)
      end
    end

    context 'with invalid token' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
      end

      it 'returns unauthorized status' do
        put :update, format: :json, params: {
          name: 'Test User',
          email: ''
        }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'DELETE #destroy' do
    context 'with valid attributes' do
      it 'returns ok status' do
        delete :destroy, format: :json, params: {
          id: user.id
        }
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid token' do
      before do
        request.headers['ADMIN_TOKEN'] = nil
      end

      it 'returns unauthorized status' do
        delete :destroy, format: :json, params: {
          id: user.id
        }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
