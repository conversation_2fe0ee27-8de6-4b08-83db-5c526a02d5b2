require 'rails_helper'

RSpec.describe External::UsersController, type: :controller do
  let(:user) { create(:user) }

  before { request.headers['Authorization'] = "Basic #{Company.first.api_key}" }

  describe 'POST create' do
    let(:service) { double(:service, create_unconfirmed: true, success: true, errors: ['Warning'], record: record) }
    let(:department) { create(:department) }
    let(:department2) { create(:department) }
    let(:record) { double(User, id: SecureRandom.uuid) }
    let(:parameters) do
      {
        'name' => 'Foobar', 'password' => 'ygP_RWY|5h824<', 'password_confirmation' => 'ygP_RWY|5h824<', 'email' => '<EMAIL>',
        'limited' => 'true', 'coordinator' => 'true', 'chat_enabled' => 'true', 'department_ids' => [department.id, department2.id]
      }.with_indifferent_access
    end

    before { allow(UserService).to receive(:new).and_return(service) }

    context 'with invalid parameters' do
      let(:service) { double(:service, create_unconfirmed: true, success: false, errors: ['Error']) }
      let(:parameters) { { 'name' => '', 'email' => '' } }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters, format: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters, format: :json

        expect_json(errors: service.errors)
      end
    end

    context 'with invalid format' do
      let(:service) { double(:service, create_unconfirmed: true, success: false, errors: ['Error']) }
      let(:parameters) { { 'name' => '', 'email' => '' } }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters, format: :xml

        expect(response).to have_http_status(:not_acceptable)
      end
    end

    context 'with invalid provider' do
      let(:invalid_provider) { 'invalid_provider' }

      it 'returns unprocessable entity status for invalid providers' do
        post :create, xhr: true, params: parameters.merge(provider: invalid_provider), format: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the invalid provider error message' do
        post :create, xhr: true, params: parameters.merge(provider: invalid_provider), format: :json

        expect_json(errors: I18n.t("api.v1.users.errors.invalid_provider"))
      end
    end

    context 'with valid provider' do
      let(:valid_provider) { 'email' }

      it 'allows valid providers and blank values' do
        allow(UserService).to receive(:new).and_return(service)

        post :create, xhr: true, params: parameters.merge(provider: valid_provider), format: :json

        expect(response).to have_http_status(:created)
      end
    end

    context 'when provider is email' do
      context 'with missing password' do
        it 'returns unprocessable entity with password required error' do
          post :create, xhr: true, params: parameters.merge(
            provider: 'email',
            password: '',
            password_confirmation: 'ygP_RWY|5h824<'
          ), format: :json

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)['errors']).to include(I18n.t("api.v1.users.errors.password_required"))
        end
      end

      context 'with missing password confirmation' do
        it 'returns unprocessable entity with confirmation password required error' do
          post :create, xhr: true, params: parameters.merge(
            provider: 'email',
            password: 'ygP_RWY|5h824<',
            password_confirmation: ''
          ), format: :json

          expect(response).to have_http_status(:unprocessable_entity)
          expect(JSON.parse(response.body)['errors']).to include(I18n.t("api.v1.users.errors.confirmation_password_required"))
        end
      end

      context 'with both password and confirmation missing' do
        it 'returns unprocessable entity with both error messages' do
          post :create, xhr: true, params: parameters.merge(
            provider: 'email',
            password: '',
            password_confirmation: ''
          ), format: :json

          expect(response).to have_http_status(:unprocessable_entity)
          response_errors = JSON.parse(response.body)['errors']
          expect(response_errors).to include(I18n.t("api.v1.users.errors.password_required"))
          expect(response_errors).to include(I18n.t("api.v1.users.errors.confirmation_password_required"))
        end
      end

      context 'with valid password and confirmation' do
        it 'proceeds with user creation' do
          allow(UserService).to receive(:new).and_return(service)

          post :create, xhr: true, params: parameters.merge(provider: 'email'), format: :json

          expect(response).to have_http_status(:created)
        end
      end
    end

    context 'with valid parameters' do
      it 'initializes the user service' do
        expect(UserService).to receive(:new).with(ActionController::Parameters.new(parameters.except(:id)).permit(
          :name, :email, :password, :password_confirmation, :limited, :coordinator, :notification, :approved, :chat_enabled, block_menus: [], department_ids: []).merge(uid: parameters[:email])
        ).and_call_original

        post :create, xhr: true, params: parameters, format: :json
      end

      it 'creates the user' do
        expect(service).to receive(:create_unconfirmed)

        post :create, xhr: true, params: parameters, format: :json
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters, format: :json

        expect(response).to have_http_status(:created)
      end

      it 'returns the user id' do
        post :create, xhr: true, params: parameters, format: :json

        expect_json(id: record.id)
      end

      context 'when create_confirmed is truthy' do
        let(:parameters) do
          {
            'name' => 'Foobar', 'password' => 'ygP_RWY|5h824<', 'password_confirmation' => 'ygP_RWY|5h824<', 'email' => '<EMAIL>',
            'limited' => 'true', 'coordinator' => 'true', 'chat_enabled' => 'true', 'department_ids' => [department.id, department2.id],
            'create_confirmed' => 'true'
          }.with_indifferent_access
        end

        it 'creates a confirmed user' do
          expect(service).to receive(:create)

          post :create, xhr: true, params: parameters, format: :json
        end
      end
    end
  end

  describe 'PUT update' do
    let(:service) { double(:service, update: true, success: true, errors: ['Warning']) }
    let(:department) { create(:department) }
    let(:department2) { create(:department) }
    let(:user) { create(:user) }
    let(:parameters) do
      {
        'name' => 'Foobar', 'password' => 'ygP_RWY|5h824<', 'password_confirmation' => 'ygP_RWY|5h824<', 'email' => '<EMAIL>',
        'limited' => 'true', 'coordinator' => 'true', 'chat_enabled' => 'true', 'department_ids' => [department.id, department2.id], 'id' => user.id
      }.with_indifferent_access
    end

    before { allow(UserService).to receive(:new).and_return(service) }

    context 'with invalid parameters' do
      let(:service) { double(:service, update: true, success: false, errors: ['Error']) }
      let(:parameters) { { 'name' => '', 'email' => '', 'id' => user.id } }

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters, format: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        put :update, xhr: true, params: parameters, format: :json

        expect_json(errors: service.errors)
      end
    end

    context 'with invalid format' do
      let(:service) { double(:service, update: true, success: false, errors: ['Error']) }
      let(:parameters) { { 'name' => '', 'email' => '', 'id' => user.id } }

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters, format: :xml

        expect(response).to have_http_status(:not_acceptable)
      end
    end

    context 'with valid parameters' do
      it 'initializes the user service' do
        expect(UserService).to receive(:new).with(ActionController::Parameters.new(parameters.except(:id)).permit(
          :name, :email, :password, :password_confirmation, :limited, :coordinator, :notification, :approved, :chat_enabled, block_menus: [], department_ids: [])
        ).and_call_original

        put :update, xhr: true, params: parameters, format: :json
      end

      it 'updates the user' do
        expect(service).to receive(:update).with(user.id)

        put :update, xhr: true, params: parameters, format: :json
      end

      it 'returns the ok status' do
        put :update, xhr: true, params: parameters, format: :json

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'DELETE destroy' do
    let(:parameters) { { id: user.id } }

    context 'with error' do
      let(:user_service) { double(:user_service, destroy: false, success: false, errors: ['foo']) }

      before do
        allow(UserService).to receive(:new).and_return(user_service)
      end

      it 'returns bad request status' do
        delete :destroy, xhr: true, params: parameters, format: :json

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        delete :destroy, xhr: true, params: parameters, format: :json

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the user service' do
        expect(UserService).to receive(:new).and_call_original

        delete :destroy, xhr: true, params: parameters, format: :json
      end

      it 'destroys the user' do
        expect_any_instance_of(UserService).to receive(:destroy).with(parameters[:id]).and_call_original

        delete :destroy, xhr: true, params: parameters, format: :json
      end

      it 'returns no content status' do
        delete :destroy, xhr: true, params: parameters, format: :json

        expect(response).to have_http_status(:no_content)
      end
    end
  end
end
